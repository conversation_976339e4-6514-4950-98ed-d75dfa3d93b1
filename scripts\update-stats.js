#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 统计文档字数和章节数的脚本
 * 自动更新 _coverpage.md 中的统计信息
 */

// 配置
const CONFIG = {
  // 需要统计的文件扩展名
  extensions: ['.md'],
  // 排除的文件
  excludeFiles: ['_coverpage.md', '_sidebar.md'],
  // 排除的目录
  excludeDirs: ['node_modules', '.git', 'scripts'],
  // 根目录
  rootDir: path.join(__dirname, '..'),
  // 封面页文件
  coverpagePath: path.join(__dirname, '..', '_coverpage.md')
};

/**
 * 递归获取所有 Markdown 文件
 */
function getAllMarkdownFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 跳过排除的目录
      if (!CONFIG.excludeDirs.includes(item)) {
        getAllMarkdownFiles(fullPath, files);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      const filename = path.basename(item);
      
      // 检查文件扩展名和排除列表
      if (CONFIG.extensions.includes(ext) && !CONFIG.excludeFiles.includes(filename)) {
        files.push(fullPath);
      }
    }
  }
  
  return files;
}

/**
 * 统计文件字数（中文字符 + 英文单词）
 */
function countWords(content) {
  // 移除 Markdown 语法
  let text = content
    .replace(/```[\s\S]*?```/g, '') // 代码块
    .replace(/`[^`]*`/g, '') // 行内代码
    .replace(/!\[.*?\]\(.*?\)/g, '') // 图片
    .replace(/\[.*?\]\(.*?\)/g, '') // 链接
    .replace(/#{1,6}\s/g, '') // 标题
    .replace(/[*_]{1,2}([^*_]+)[*_]{1,2}/g, '$1') // 粗体斜体
    .replace(/^\s*[-*+]\s/gm, '') // 列表
    .replace(/^\s*\d+\.\s/gm, '') // 有序列表
    .replace(/^\s*>\s/gm, '') // 引用
    .replace(/\|.*\|/g, '') // 表格
    .replace(/---+/g, '') // 分隔线
    .replace(/<[^>]*>/g, '') // HTML 标签
    .trim();
  
  // 统计中文字符
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  
  // 统计英文单词
  const englishWords = text
    .replace(/[\u4e00-\u9fff]/g, ' ') // 将中文字符替换为空格
    .split(/\s+/)
    .filter(word => word.length > 0 && /[a-zA-Z]/.test(word))
    .length;
  
  return chineseChars + englishWords;
}

/**
 * 统计章节数（只统计主要章节）
 */
function countSections(content) {
  // 移除HTML标签和注释
  let cleanContent = content
    .replace(/<!--[\s\S]*?-->/g, '') // 移除HTML注释
    .replace(/<[^>]*>/g, ''); // 移除HTML标签

  // 只统计 # ## 级别的主要标题
  const headers = cleanContent.match(/^#{1,2}\s+.+$/gm) || [];

  return headers.length;
}

/**
 * 格式化数字显示
 */
function formatNumber(num) {
  if (num >= 10000) {
    return Math.floor(num / 1000) + 'k+';
  } else if (num >= 1000) {
    return Math.floor(num / 100) / 10 + 'k+';
  } else {
    return num.toString();
  }
}

/**
 * 计算阅读时长（按每分钟200字计算）
 */
function calculateReadingTime(wordCount) {
  const minutes = Math.ceil(wordCount / 200);
  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`;
  }
  return `${minutes}分钟`;
}

/**
 * 主函数
 */
function main() {
  try {
    console.log('🔍 开始统计文档信息...');
    
    // 获取所有 Markdown 文件
    const files = getAllMarkdownFiles(CONFIG.rootDir);
    console.log(`📁 找到 ${files.length} 个文档文件`);
    
    let totalWords = 0;
    let totalSections = 0;
    
    // 统计每个文件
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const words = countWords(content);
      const sections = countSections(content);
      
      totalWords += words;
      totalSections += sections;
      
      const relativePath = path.relative(CONFIG.rootDir, file);
      console.log(`📄 ${relativePath}: ${words} 字, ${sections} 章节`);
    }
    
    console.log(`\n📊 统计结果:`);
    console.log(`   总字数: ${totalWords}`);
    console.log(`   总章节: ${totalSections}`);
    console.log(`   阅读时长: ${calculateReadingTime(totalWords)}`);
    
    // 更新封面页
    updateCoverpage(totalWords, totalSections, calculateReadingTime(totalWords));
    
    console.log('\n✅ 统计完成！');
    
  } catch (error) {
    console.error('❌ 统计过程中出现错误:', error.message);
    process.exit(1);
  }
}

/**
 * 更新封面页统计信息
 */
function updateCoverpage(wordCount, sectionCount, readingTime) {
  try {
    let content = fs.readFileSync(CONFIG.coverpagePath, 'utf8');
    
    // 获取当前日期
    const currentDate = new Date().getFullYear();
    
    // 更新统计信息
    content = content
      .replace(
        /📊 文档字数: [^<]+/,
        `📊 文档字数: ${formatNumber(wordCount)}`
      )
      .replace(
        /📖 章节数: [^<]+/,
        `📖 章节数: ${sectionCount}+`
      )
      .replace(
        /⏱️ 阅读时长: [^<]+/,
        `⏱️ 阅读时长: ${readingTime}`
      )
      .replace(
        /🔄 最后更新: [^<]+/,
        `🔄 最后更新: ${currentDate}`
      );
    
    fs.writeFileSync(CONFIG.coverpagePath, content, 'utf8');
    console.log('📝 封面页统计信息已更新');
    
  } catch (error) {
    console.error('❌ 更新封面页时出现错误:', error.message);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  getAllMarkdownFiles,
  countWords,
  countSections,
  formatNumber,
  calculateReadingTime,
  updateCoverpage
};
