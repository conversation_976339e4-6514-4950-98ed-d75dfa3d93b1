#!/bin/sh

# Git pre-commit hook
# 在提交前自动更新文档统计信息

echo "🔄 正在更新文档统计信息..."

# 检查是否有 Node.js
if ! command -v node >/dev/null 2>&1; then
    echo "⚠️  警告: 未找到 Node.js，跳过统计信息更新"
    exit 0
fi

# 检查是否有统计脚本
if [ ! -f "scripts/update-stats.js" ]; then
    echo "⚠️  警告: 未找到统计脚本，跳过统计信息更新"
    exit 0
fi

# 运行统计脚本
node scripts/update-stats.js

# 检查是否有变更
if git diff --quiet _coverpage.md; then
    echo "✅ 统计信息无变更"
else
    echo "📝 统计信息已更新，添加到提交中..."
    git add _coverpage.md
fi

echo "✅ 预提交检查完成"
exit 0
