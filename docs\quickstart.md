# 快速开始

<!-- 章节统计信息 -->
<div style="background: linear-gradient(135deg, #f0fffe 0%, #e6fffe 100%); border-left: 4px solid #3fcfbb; padding: 15px 20px; margin: 20px 0; border-radius: 8px;">
  <div style="display: flex; gap: 20px; flex-wrap: wrap; align-items: center;">
    <span style="color: #2aa698; font-weight: 600; font-size: 0.9em;">📊 本章统计</span>
    <span style="background: #3fcfbb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">字数: ~2,800</span>
    <span style="background: #33cabb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">阅读时长: ~8分钟</span>
    <span style="background: #2aa698; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">难度: 入门</span>
  </div>
</div>

本指南将帮助您快速上手一花CCP项目，在几分钟内完成基本配置并开始使用。

## 前置要求

在开始之前，请确保您的系统满足以下要求：

- **操作系统**: Windows 10+, macOS 10.14+, 或 Linux (Ubuntu 18.04+)
- **内存**: 最少 4GB RAM，推荐 8GB+
- **存储空间**: 至少 2GB 可用空间
- **网络**: 稳定的互联网连接

## 安装步骤

### 1. 下载安装包

从官方网站下载最新版本的安装包：

```bash
# 使用wget下载（Linux/macOS）
wget https://releases.yihua-ccp.com/latest/yihua-ccp-latest.tar.gz

# 或使用curl下载
curl -O https://releases.yihua-ccp.com/latest/yihua-ccp-latest.tar.gz
```

### 2. 解压安装包

```bash
# 解压文件
tar -xzf yihua-ccp-latest.tar.gz

# 进入目录
cd yihua-ccp
```

### 3. 运行安装脚本

```bash
# Linux/macOS
./install.sh

# Windows
install.bat
```

### 4. 启动服务

```bash
# 启动一花CCP服务
./start.sh

# 或使用系统服务方式启动
systemctl start yihua-ccp
```

## 首次配置

### 1. 访问管理界面

安装完成后，打开浏览器访问：

```
http://localhost:8080
```

### 2. 初始化设置

1. **管理员账户设置**
   - 用户名：admin
   - 密码：请设置强密码

2. **基本配置**
   - 系统名称：一花CCP
   - 时区设置：Asia/Shanghai
   - 语言设置：中文

3. **数据库配置**
   - 数据库类型：MySQL/PostgreSQL
   - 连接地址：localhost:3306
   - 数据库名：yihua_ccp

### 3. 验证安装

完成配置后，系统会自动进行健康检查：

- ✅ 数据库连接正常
- ✅ 服务端口监听正常
- ✅ 基础功能模块加载成功

## 基本使用

### 创建第一个项目

1. 登录系统后，点击"新建项目"
2. 填写项目基本信息：
   - 项目名称
   - 项目描述
   - 负责人
3. 选择项目模板（可选）
4. 点击"创建"完成

### 邀请团队成员

1. 进入项目设置页面
2. 点击"成员管理"
3. 输入成员邮箱地址
4. 选择权限级别
5. 发送邀请

## 下一步

恭喜！您已经成功完成了一花CCP的基本设置。接下来您可以：

- 查看[功能特性](features.md)了解更多功能
- 阅读[使用手册](user-guide.md)深入学习
- 参考[API文档](api.md)进行系统集成

## 常见问题

**Q: 安装过程中遇到权限错误怎么办？**
A: 请使用管理员权限运行安装脚本，或使用 `sudo` 命令。

**Q: 无法访问管理界面？**
A: 请检查防火墙设置，确保8080端口已开放。

**Q: 数据库连接失败？**
A: 请确认数据库服务已启动，连接信息正确。

更多问题请查看[常见问题](faq.md)页面。
