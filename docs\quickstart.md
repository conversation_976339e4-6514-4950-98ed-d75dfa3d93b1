# 快速开始

<!-- 章节统计信息 -->
<div style="background: linear-gradient(135deg, #f0fffe 0%, #e6fffe 100%); border-left: 4px solid #3fcfbb; padding: 15px 20px; margin: 20px 0; border-radius: 8px;">
  <div style="display: flex; gap: 20px; flex-wrap: wrap; align-items: center;">
    <span style="color: #2aa698; font-weight: 600; font-size: 0.9em;">📊 本章统计</span>
    <span style="background: #3fcfbb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">字数: ~2,800</span>
    <span style="background: #33cabb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">阅读时长: ~8分钟</span>
    <span style="background: #2aa698; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">难度: 入门</span>
  </div>
</div>

本指南将帮助您快速上手一花CCPROXY系统商业版，在几分钟内完成基本配置并开始管理您的CCProxy代理服务。

## 前置要求

在开始之前，请确保您的系统满足以下要求：

- **操作系统**: Windows Server 2012+, Windows 10+, 或 Linux (Ubuntu 18.04+)
- **内存**: 最少 2GB RAM，推荐 4GB+
- **存储空间**: 至少 1GB 可用空间
- **网络**: 稳定的互联网连接
- **CCProxy软件**: 已安装并运行的CCProxy服务器
- **数据库**: MySQL 5.7+ 或 MariaDB 10.3+

## 安装步骤

### 1. 下载安装包

从官方网站下载最新版本的安装包：

```bash
# Windows 用户下载
https://raw.githubusercontent.com/yeuxuan/yihua_ccproxy_business/refs/heads/main/yihua_ccproxy_business_20250608154905.zip
# Linux 用户下载
wget https://raw.githubusercontent.com/yeuxuan/yihua_ccproxy_business/refs/heads/main/yihua_ccproxy_business_20250608154905.zip
```

### 2. 解压安装包

```bash
# Windows: 解压到指定目录
# 例如: C:\yihua-ccproxy\

# Linux: 解压文件
tar -xzf yihua-ccproxy-linux.tar.gz
cd yihua-ccproxy
```

### 3. 配置数据库

创建MySQL数据库并导入初始数据：

```sql
-- 创建数据库
CREATE DATABASE yihua_ccproxy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'ccproxy_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON yihua_ccproxy.* TO 'ccproxy_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. 配置应用

编辑配置文件 `config/database.php`：

```php
<?php
return [
    'host' => 'localhost',
    'port' => 3306,
    'database' => 'yihua_ccproxy',
    'username' => 'ccproxy_user',
    'password' => 'your_password',
];
```

### 5. 启动服务

```bash
# Windows: 双击运行
start.bat

# Linux: 运行启动脚本
./start.sh

# 或使用系统服务方式启动（Linux）
systemctl start yihua-ccproxy
```

## 首次配置

### 1. 访问管理界面

安装完成后，打开浏览器访问：

```text
http://localhost:8080
```

### 2. 初始化设置

1. **管理员账户设置**
   - 用户名：admin
   - 密码：请设置强密码（建议包含大小写字母、数字和特殊字符）

2. **基本配置**
   - 系统名称：一花CCPROXY系统商业版
   - 时区设置：Asia/Shanghai
   - 语言设置：中文

3. **CCProxy服务器配置**
   - 服务器地址：CCProxy服务器IP地址
   - 管理端口：CCProxy管理端口（默认8888）
   - 认证信息：CCProxy管理员用户名和密码

### 3. 添加第一个CCProxy服务器

1. 进入"服务器管理"页面
2. 点击"添加服务器"
3. 填写服务器信息：
   - 服务器名称：自定义名称
   - IP地址：CCProxy服务器IP
   - 管理端口：通常为8888
   - 用户名/密码：CCProxy管理员凭据
4. 点击"测试连接"验证配置
5. 保存配置

### 4. 验证安装

完成配置后，系统会自动进行健康检查：

- ✅ 数据库连接正常
- ✅ Web服务端口监听正常
- ✅ CCProxy服务器连接正常
- ✅ 基础功能模块加载成功

## 基本使用

### 创建第一个套餐

1. 登录系统后，进入"套餐管理"
2. 点击"添加套餐"
3. 填写套餐信息：
   - 套餐名称（如：月卡、季卡、年卡）
   - 使用时长（天数）
   - 套餐价格
   - 套餐描述（支持Markdown格式）
4. 选择适用的服务器
5. 点击"保存"完成

### 生成卡密

1. 进入"卡密管理"页面
2. 点击"生成卡密"
3. 选择套餐类型
4. 设置生成数量
5. 点击"生成"
6. 可选择导出卡密到Excel文件

### 添加代理商

1. 进入"代理商管理"
2. 点击"添加代理商"
3. 填写代理商信息：
   - 代理商账号
   - 密码
   - 联系方式
   - 佣金比例
4. 设置权限级别
5. 保存配置

## 下一步

恭喜！您已经成功完成了一花CCPROXY系统商业版的基本设置。接下来您可以：

- 查看[功能特性](features.md)了解更多功能
- 阅读[使用手册](user-guide.md)深入学习系统操作
- 参考[API文档](api.md)进行系统集成
- 查看[开发文档](development.md)了解二次开发

## 常见问题

**Q: 安装过程中遇到权限错误怎么办？**
A: Windows用户请以管理员身份运行，Linux用户请使用 `sudo` 命令。

**Q: 无法访问管理界面？**
A: 请检查防火墙设置，确保8080端口已开放，并确认服务已正常启动。

**Q: 数据库连接失败？**
A: 请确认MySQL服务已启动，数据库名称、用户名、密码配置正确。

**Q: 无法连接CCProxy服务器？**
A: 请检查CCProxy服务器是否正常运行，管理端口是否开放，认证信息是否正确。

**Q: 卡密生成失败？**
A: 请确认套餐配置正确，数据库连接正常，并检查系统日志获取详细错误信息。

更多问题请查看[常见问题](faq.md)页面。
