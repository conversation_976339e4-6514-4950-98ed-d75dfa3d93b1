# 一花CCP项目文档结构说明

## 📁 项目目录结构

```
yihua_ccp_doc/
├── README.md                    # 项目主页内容
├── index.html                   # docsify配置文件
├── _coverpage.md               # 封面页配置
├── _sidebar.md                 # 侧边栏导航配置
├── PROJECT_STRUCTURE.md        # 项目结构说明（本文件）
├── docs/                       # 文档内容目录
│   ├── quickstart.md           # 快速开始指南
│   ├── features.md             # 功能特性介绍
│   ├── installation.md         # 安装部署指南
│   ├── user-guide.md           # 使用手册
│   ├── api.md                  # API文档
│   ├── development.md          # 开发指南
│   ├── faq.md                  # 常见问题
│   └── changelog.md            # 更新日志
└── assets/                     # 静态资源目录
    ├── css/                    # 样式文件
    │   └── style.css           # 自定义样式
    └── images/                 # 图片资源
        └── logo.svg            # 项目Logo
```

## 📋 文件说明

### 核心配置文件

- **index.html**: docsify的主配置文件，包含插件配置和基本设置
- **_coverpage.md**: 封面页内容，包含项目介绍和快速导航
- **_sidebar.md**: 侧边栏导航配置，定义文档结构
- **README.md**: 项目主页内容，作为文档首页

### 文档内容 (docs/)

- **quickstart.md**: 快速开始指南，帮助用户快速上手
- **features.md**: 功能特性详细介绍
- **installation.md**: 详细的安装部署指南
- **user-guide.md**: 完整的用户操作手册
- **api.md**: API接口文档和示例
- **development.md**: 开发环境搭建和开发指南
- **faq.md**: 常见问题和解决方案
- **changelog.md**: 版本更新历史

### 静态资源 (assets/)

- **css/style.css**: 自定义样式文件，包含3D效果和青绿色主题
- **images/logo.svg**: 项目Logo文件

## 🚀 启动方式

1. 确保已安装docsify-cli：
   ```bash
   npm install -g docsify-cli
   ```

2. 在项目根目录启动服务：
   ```bash
   docsify serve .
   ```

3. 访问 http://localhost:3000 查看文档

## 🎨 主题特色

- **3D立体效果**: 封面页采用现代3D设计
- **青绿色主题**: 使用 #3fcfbb、#33cabb、#2aa698 配色方案
- **响应式设计**: 完美适配桌面和移动设备
- **现代化UI**: 毛玻璃效果、动态动画、立体按钮

## 📝 维护说明

### 添加新文档

1. 在 `docs/` 目录下创建新的 `.md` 文件
2. 在 `_sidebar.md` 中添加对应的导航链接
3. 确保链接路径正确（相对于根目录）

### 修改样式

- 主要样式文件：`assets/css/style.css`
- 包含封面3D效果、配色方案、响应式布局等

### 更新配置

- docsify配置：`index.html`
- 侧边栏导航：`_sidebar.md`
- 封面页内容：`_coverpage.md`

## 🔧 技术栈

- **docsify**: 文档生成框架
- **CSS3**: 3D效果和动画
- **JavaScript**: 交互功能
- **SVG**: 矢量图标和Logo

## 📞 技术支持

如需技术支持或有任何问题，请联系开发团队。
