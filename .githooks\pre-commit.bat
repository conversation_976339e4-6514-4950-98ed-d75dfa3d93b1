@echo off
REM Git pre-commit hook for Windows
REM 在提交前自动更新文档统计信息

echo 🔄 正在更新文档统计信息...

REM 检查是否有 Node.js
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  警告: 未找到 Node.js，跳过统计信息更新
    exit /b 0
)

REM 检查是否有统计脚本
if not exist "scripts\update-stats.js" (
    echo ⚠️  警告: 未找到统计脚本，跳过统计信息更新
    exit /b 0
)

REM 运行统计脚本
node scripts\update-stats.js
if %errorlevel% neq 0 (
    echo ❌ 统计脚本运行失败
    exit /b 1
)

REM 检查是否有变更
git diff --quiet _coverpage.md
if %errorlevel% neq 0 (
    echo 📝 统计信息已更新，添加到提交中...
    git add _coverpage.md
) else (
    echo ✅ 统计信息无变更
)

echo ✅ 预提交检查完成
exit /b 0
