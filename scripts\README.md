# 文档统计自动化脚本

## 概述

这个脚本用于自动统计项目中所有 Markdown 文档的字数、章节数等信息，并自动更新 `_coverpage.md` 中的统计数据，避免硬编码问题。

## 功能特性

- 🔢 **自动字数统计**: 智能统计中文字符和英文单词
- 📖 **章节数统计**: 统计所有标题数量
- ⏱️ **阅读时长计算**: 基于字数自动计算预估阅读时间
- 🎯 **智能过滤**: 自动排除 Markdown 语法、代码块等非正文内容
- 🔄 **自动更新**: 一键更新封面页统计信息
- 📁 **灵活配置**: 可配置包含/排除的文件和目录

## 使用方法

### 手动运行

```bash
# 安装依赖（如果需要）
npm install

# 运行统计脚本
npm run update-stats

# 或直接运行
node scripts/update-stats.js
```

### 自动化集成

#### 1. Git Hook 集成

设置 Git Hook 在每次提交前自动更新统计信息：

```bash
# 设置 Git Hook 路径
git config core.hooksPath .githooks

# 给 hook 文件执行权限（Linux/Mac）
chmod +x .githooks/pre-commit
```

#### 2. 开发流程集成

在 `package.json` 中已配置相关脚本：

```json
{
  "scripts": {
    "update-stats": "node scripts/update-stats.js",
    "precommit": "npm run update-stats"
  }
}
```

## 配置说明

脚本配置位于 `scripts/update-stats.js` 文件顶部的 `CONFIG` 对象：

```javascript
const CONFIG = {
  // 需要统计的文件扩展名
  extensions: ['.md'],
  
  // 排除的文件
  excludeFiles: ['_coverpage.md', '_sidebar.md'],
  
  // 排除的目录
  excludeDirs: ['node_modules', '.git', 'scripts'],
  
  // 其他配置...
};
```

### 可配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `extensions` | 要统计的文件扩展名 | `['.md']` |
| `excludeFiles` | 排除的文件名 | `['_coverpage.md', '_sidebar.md']` |
| `excludeDirs` | 排除的目录名 | `['node_modules', '.git', 'scripts']` |

## 统计算法

### 字数统计

1. **预处理**: 移除 Markdown 语法元素
   - 代码块 (\`\`\`...\`\`\`)
   - 行内代码 (\`...\`)
   - 图片链接 (![...](...))
   - 普通链接 ([...](...))
   - 标题标记 (# ## ###)
   - 粗体斜体标记 (* ** _)
   - 列表标记 (- * + 1.)
   - 引用标记 (>)
   - 表格和分隔线
   - HTML 标签

2. **字数计算**:
   - 中文字符: 每个字符计为 1 字
   - 英文单词: 按空格分隔计算单词数

### 章节统计

统计所有级别的标题数量 (`# ## ### #### ##### ######`)

### 阅读时长

按每分钟 200 字的阅读速度计算预估时间

## 输出格式

脚本会自动更新 `_coverpage.md` 中的以下统计信息：

- 📊 文档字数: 自动格式化显示（如 50k+）
- 📖 章节数: 总章节数 + "+"
- ⏱️ 阅读时长: 格式化的时间（如 "3小时" 或 "45分钟"）
- 🔄 最后更新: 当前年份

## 故障排除

### 常见问题

1. **权限错误**: 确保脚本有执行权限
2. **路径错误**: 确保在项目根目录运行脚本
3. **编码问题**: 确保所有 Markdown 文件使用 UTF-8 编码

### 调试模式

可以修改脚本添加更详细的日志输出来调试问题。

## 扩展功能

### 添加新的统计项

可以在脚本中添加新的统计功能，如：

- 图片数量统计
- 代码块数量统计
- 外部链接数量统计
- 文件大小统计

### 自定义格式化

可以修改 `formatNumber` 和 `calculateReadingTime` 函数来自定义数字和时间的显示格式。

## 版本历史

- v1.0.0: 初始版本，支持基本的字数和章节统计
