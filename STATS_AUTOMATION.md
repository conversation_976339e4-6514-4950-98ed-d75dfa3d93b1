# 文档统计自动化系统

## 问题解决

✅ **已解决**: 封面页中的文档字数统计不再硬编码，现在通过自动化脚本动态生成。

## 系统概述

这个自动化系统解决了文档统计信息硬编码的问题，提供了以下功能：

### 🎯 核心功能

1. **自动字数统计**: 智能统计所有 Markdown 文档的字数
2. **章节数统计**: 统计所有标题数量
3. **阅读时长计算**: 基于字数自动计算预估阅读时间
4. **自动更新封面页**: 一键更新 `_coverpage.md` 中的统计信息

### 📊 当前统计结果

运行脚本后的实际统计数据：
- 📊 文档字数: 7.7k+ (从硬编码的 50,000+ 更新为实际的 7,700 字)
- 📖 章节数: 327+ (实际统计结果)
- ⏱️ 阅读时长: 39分钟 (基于实际字数计算)
- 🔄 最后更新: 2025 (自动更新年份)

## 🚀 快速使用

### 手动更新统计信息

```bash
# 运行统计脚本
npm run update-stats

# 或直接运行
node scripts/update-stats.js
```

### 自动化集成

1. **Git Hook 自动化** (推荐)
   ```bash
   # 设置 Git Hook 路径
   git config core.hooksPath .githooks
   ```
   
   现在每次 `git commit` 时会自动更新统计信息。

2. **开发时自动化**
   ```bash
   # 在开发过程中随时更新
   npm run build-stats
   ```

## 📁 文件结构

```
├── scripts/
│   ├── update-stats.js      # 主要统计脚本
│   └── README.md           # 详细使用说明
├── .githooks/
│   ├── pre-commit          # Linux/Mac Git Hook
│   └── pre-commit.bat      # Windows Git Hook
├── package.json            # NPM 脚本配置
├── _coverpage.md          # 自动更新的封面页
└── STATS_AUTOMATION.md    # 本文档
```

## 🔧 配置选项

脚本支持灵活配置，可以在 `scripts/update-stats.js` 中修改：

```javascript
const CONFIG = {
  extensions: ['.md'],                              // 统计的文件类型
  excludeFiles: ['_coverpage.md', '_sidebar.md'],  // 排除的文件
  excludeDirs: ['node_modules', '.git', 'scripts'] // 排除的目录
};
```

## 🎨 统计算法

### 字数统计逻辑
- 自动移除 Markdown 语法元素（代码块、链接、标题标记等）
- 中文字符按字计算，英文按单词计算
- 智能过滤，只统计实际内容

### 格式化显示
- 1000+ 字显示为 "1k+"
- 10000+ 字显示为 "10k+"
- 阅读时长按每分钟 200 字计算

## ✨ 优势

1. **准确性**: 基于实际文档内容统计，不再依赖估算
2. **自动化**: 无需手动维护，减少人为错误
3. **实时性**: 文档更新时统计信息自动同步
4. **灵活性**: 支持配置和扩展
5. **可维护性**: 代码结构清晰，易于维护和扩展

## 🔄 工作流程

1. **开发阶段**: 编写或修改文档
2. **提交前**: Git Hook 自动运行统计脚本
3. **自动更新**: 脚本更新 `_coverpage.md` 中的统计信息
4. **提交**: 更新后的统计信息一起提交

## 📈 未来扩展

可以考虑添加的功能：
- 图片数量统计
- 代码示例数量统计
- 外部链接数量统计
- 多语言支持
- 更详细的统计报告

## 🛠️ 故障排除

如果遇到问题：

1. **确保 Node.js 已安装**: `node --version`
2. **检查脚本权限**: 确保脚本文件可执行
3. **查看详细日志**: 脚本会输出详细的统计过程
4. **手动运行测试**: 先手动运行脚本确认功能正常

---

**总结**: 通过这个自动化系统，文档统计信息不再硬编码，而是基于实际内容动态生成，确保了数据的准确性和实时性。
