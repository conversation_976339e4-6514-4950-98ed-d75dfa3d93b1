# API文档

<!-- 章节统计信息 -->
<div style="background: linear-gradient(135deg, #f0fffe 0%, #e6fffe 100%); border-left: 4px solid #3fcfbb; padding: 15px 20px; margin: 20px 0; border-radius: 8px;">
  <div style="display: flex; gap: 20px; flex-wrap: wrap; align-items: center;">
    <span style="color: #2aa698; font-weight: 600; font-size: 0.9em;">📊 本章统计</span>
    <span style="background: #3fcfbb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">字数: ~4,500</span>
    <span style="background: #33cabb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">阅读时长: ~15分钟</span>
    <span style="background: #2aa698; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">难度: 高级</span>
  </div>
</div>

一花CCP提供完整的RESTful API接口，支持第三方系统集成和自定义开发。

## 接口概述

### 基础信息

- **API版本**: v1.0
- **基础URL**: `https://your-domain.com/api/v1`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求方式**: GET, POST, PUT, DELETE

### 认证方式

API使用Bearer Token认证方式：

```http
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 获取访问令牌

```http
POST /auth/token
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## 客户管理API

### 获取客户列表

```http
GET /customers
```

**查询参数**:
- `page` (int): 页码，默认1
- `size` (int): 每页数量，默认20
- `search` (string): 搜索关键词
- `group_id` (int): 客户分组ID

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 150,
    "page": 1,
    "size": 20,
    "items": [
      {
        "id": 1,
        "name": "张三",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "company": "ABC公司",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 获取客户详情

```http
GET /customers/{id}
```

**路径参数**:
- `id` (int): 客户ID

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "company": "ABC公司",
    "address": "北京市朝阳区",
    "tags": ["VIP", "重要客户"],
    "group_id": 1,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

### 创建客户

```http
POST /customers
Content-Type: application/json

{
  "name": "李四",
  "email": "<EMAIL>",
  "phone": "13900139000",
  "company": "XYZ公司",
  "address": "上海市浦东新区",
  "tags": ["新客户"],
  "group_id": 2
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "客户创建成功",
  "data": {
    "id": 2,
    "name": "李四",
    "email": "<EMAIL>",
    "phone": "13900139000",
    "company": "XYZ公司",
    "address": "上海市浦东新区",
    "tags": ["新客户"],
    "group_id": 2,
    "created_at": "2024-01-02T10:00:00Z",
    "updated_at": "2024-01-02T10:00:00Z"
  }
}
```

### 更新客户

```http
PUT /customers/{id}
Content-Type: application/json

{
  "name": "李四",
  "email": "<EMAIL>",
  "phone": "13900139000",
  "company": "XYZ公司",
  "address": "上海市浦东新区",
  "tags": ["VIP客户"],
  "group_id": 1
}
```

### 删除客户

```http
DELETE /customers/{id}
```

## 消息管理API

### 发送消息

```http
POST /messages
Content-Type: application/json

{
  "customer_id": 1,
  "content": "您好，请问有什么可以帮助您的吗？",
  "type": "text",
  "channel": "web"
}
```

**消息类型**:
- `text`: 文本消息
- `image`: 图片消息
- `file`: 文件消息
- `voice`: 语音消息

**响应示例**:
```json
{
  "code": 201,
  "message": "消息发送成功",
  "data": {
    "id": 1001,
    "customer_id": 1,
    "content": "您好，请问有什么可以帮助您的吗？",
    "type": "text",
    "channel": "web",
    "status": "sent",
    "created_at": "2024-01-02T14:30:00Z"
  }
}
```

### 获取消息历史

```http
GET /messages?customer_id=1&page=1&size=50
```

**查询参数**:
- `customer_id` (int): 客户ID
- `page` (int): 页码
- `size` (int): 每页数量
- `start_date` (string): 开始日期
- `end_date` (string): 结束日期

## 任务管理API

### 创建任务

```http
POST /tasks
Content-Type: application/json

{
  "title": "跟进客户需求",
  "description": "了解客户具体需求并提供解决方案",
  "customer_id": 1,
  "assignee_id": 10,
  "priority": "high",
  "due_date": "2024-01-10T18:00:00Z"
}
```

**优先级**:
- `low`: 低优先级
- `medium`: 中优先级
- `high`: 高优先级
- `urgent`: 紧急

### 更新任务状态

```http
PUT /tasks/{id}/status
Content-Type: application/json

{
  "status": "completed",
  "notes": "任务已完成，客户需求已确认"
}
```

**任务状态**:
- `pending`: 待处理
- `in_progress`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

## 报表统计API

### 获取客户统计

```http
GET /reports/customers/stats
```

**查询参数**:
- `start_date` (string): 开始日期
- `end_date` (string): 结束日期
- `group_by` (string): 分组方式 (day/week/month)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_customers": 1500,
    "new_customers": 50,
    "active_customers": 800,
    "growth_rate": 3.5,
    "daily_stats": [
      {
        "date": "2024-01-01",
        "new_customers": 5,
        "active_customers": 120
      }
    ]
  }
}
```

### 获取消息统计

```http
GET /reports/messages/stats
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_messages": 10000,
    "today_messages": 150,
    "avg_response_time": 300,
    "channels": {
      "web": 6000,
      "email": 3000,
      "phone": 1000
    }
  }
}
```

## Webhook事件

### 配置Webhook

```http
POST /webhooks
Content-Type: application/json

{
  "url": "https://your-app.com/webhook",
  "events": ["customer.created", "message.received"],
  "secret": "your_webhook_secret"
}
```

### 支持的事件

- `customer.created`: 客户创建
- `customer.updated`: 客户更新
- `customer.deleted`: 客户删除
- `message.sent`: 消息发送
- `message.received`: 消息接收
- `task.created`: 任务创建
- `task.completed`: 任务完成

### Webhook负载示例

```json
{
  "event": "customer.created",
  "timestamp": "2024-01-02T10:00:00Z",
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000"
  }
}
```

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ]
}
```

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率超限 |
| 500 | 服务器内部错误 |

## 限流规则

- **普通用户**: 每分钟100次请求
- **VIP用户**: 每分钟500次请求
- **企业用户**: 每分钟1000次请求

超出限制时返回429错误码。

## SDK和示例

### JavaScript SDK

```javascript
const YihuaCCP = require('yihua-ccp-sdk');

const client = new YihuaCCP({
  baseURL: 'https://your-domain.com/api/v1',
  accessToken: 'your_access_token'
});

// 获取客户列表
const customers = await client.customers.list({
  page: 1,
  size: 20
});

// 创建客户
const newCustomer = await client.customers.create({
  name: '张三',
  email: '<EMAIL>',
  phone: '13800138000'
});
```

### Python SDK

```python
from yihua_ccp import Client

client = Client(
    base_url='https://your-domain.com/api/v1',
    access_token='your_access_token'
)

# 获取客户列表
customers = client.customers.list(page=1, size=20)

# 创建客户
new_customer = client.customers.create({
    'name': '张三',
    'email': '<EMAIL>',
    'phone': '13800138000'
})
```

更多API详情和示例，请参考[开发指南](development.md)。
