# 高亮文本使用指南

<!-- 章节统计信息 -->
<div style="background: linear-gradient(135deg, #f0fffe 0%, #e6fffe 100%); border-left: 4px solid #3fcfbb; padding: 15px 20px; margin: 20px 0; border-radius: 8px;">
  <div style="display: flex; gap: 20px; flex-wrap: wrap; align-items: center;">
    <span style="color: #2aa698; font-weight: 600; font-size: 0.9em;">📊 本章统计</span>
    <span style="background: #3fcfbb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">字数: ~2,000</span>
    <span style="background: #33cabb; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">阅读时长: ~6分钟</span>
    <span style="background: #2aa698; color: white; padding: 4px 12px; border-radius: 12px; font-size: 0.8em;">难度: 基础</span>
  </div>
</div>

本文档介绍如何在一花CCPROXY系统商业版文档中使用各种高亮文本功能，让您的文档更加生动和易读。

## 基础高亮语法

### 标准高亮

使用 `==文本==` 语法创建==黄色高亮文本==，这是最常用的高亮方式。

```markdown
这是一个==重要的信息==需要注意。
```

效果：这是一个==重要的信息==需要注意。

### 带颜色的高亮

使用 `==文本=={.颜色}` 语法创建指定颜色的高亮：

```markdown
==青绿色高亮=={.cyan}
==红色高亮=={.red}
==绿色高亮=={.green}
==蓝色高亮=={.blue}
==紫色高亮=={.purple}
==橙色高亮=={.orange}
==灰色高亮=={.gray}
```

效果：
- ==青绿色高亮=={.cyan}
- ==红色高亮=={.red}
- ==绿色高亮=={.green}
- ==蓝色高亮=={.blue}
- ==紫色高亮=={.purple}
- ==橙色高亮=={.orange}
- ==灰色高亮=={.gray}

## 快捷高亮语法

为了提高书写效率，我们提供了多种快捷语法：

### 红色高亮（重要提示）

使用 `!!文本!!` 语法：

```markdown
!!这是重要警告信息!!
```

效果：!!这是重要警告信息!!

### 青绿色高亮（品牌色）

使用 `@@文本@@` 语法：

```markdown
@@一花CCPROXY系统商业版@@
```

效果：@@一花CCPROXY系统商业版@@

### 绿色高亮（成功提示）

使用 `##文本##` 语法（注意：不要与标题语法混淆）：

```markdown
##安装成功##
```

效果：##安装成功##

### 蓝色高亮（信息提示）

使用 `%%文本%%` 语法：

```markdown
%%配置信息%%
```

效果：%%配置信息%%

### 紫色高亮（特殊标记）

使用 `^^文本^^` 语法：

```markdown
^^高级功能^^
```

效果：^^高级功能^^

## 使用场景示例

### 在安装说明中使用

在安装步骤中突出重要信息：

```markdown
1. 下载安装包
2. ==确保系统满足最低要求==
3. !!备份现有数据!!
4. 运行安装程序
5. ##安装完成##
```

效果：
1. 下载安装包
2. ==确保系统满足最低要求==
3. !!备份现有数据!!
4. 运行安装程序
5. ##安装完成##

### 在配置说明中使用

突出配置项和重要参数：

```markdown
配置@@一花CCPROXY系统@@时，请注意以下参数：

- 数据库: ==MySQL 5.7+==
- 内存: %%最少 2GB RAM%%
- 存储: ^^至少 1GB 可用空间^^
- 网络: !!稳定的互联网连接!!
```

效果：

配置@@一花CCPROXY系统@@时，请注意以下参数：

- 数据库: ==MySQL 5.7+==
- 内存: %%最少 2GB RAM%%
- 存储: ^^至少 1GB 可用空间^^
- 网络: !!稳定的互联网连接!!

### 在功能介绍中使用

突出核心功能和特性：

```markdown
@@一花CCPROXY系统商业版@@提供以下核心功能：

- ==代理商管理== - 多级代理商系统
- ##卡密系统## - 自动生成和管理
- %%服务器管理%% - 统一管理多个服务器
- ^^个性化定制^^ - 灵活的配置选项
```

效果：

@@一花CCPROXY系统商业版@@提供以下核心功能：

- ==代理商管理== - 多级代理商系统
- ##卡密系统## - 自动生成和管理
- %%服务器管理%% - 统一管理多个服务器
- ^^个性化定制^^ - 灵活的配置选项

## 高亮文本最佳实践

### 1. 适度使用

- ✅ 突出关键信息和重要概念
- ❌ 过度使用导致视觉疲劳

### 2. 颜色含义

建议按以下含义使用不同颜色：

- ==黄色==：一般重要信息
- !!红色!!：警告、错误、重要提示
- ##绿色##：成功、完成、正确操作
- %%蓝色%%：信息、说明、配置项
- ^^紫色^^：高级功能、特殊标记
- @@青绿色@@：品牌相关、产品名称
- ==灰色=={.gray}：次要信息、备注

### 3. 在表格中使用

| 功能 | 状态 | 说明 |
|------|------|------|
| 代理商管理 | ##已完成## | 核心功能 |
| 卡密系统 | ==开发中== | 即将发布 |
| 服务器监控 | !!待开发!! | 计划功能 |

### 4. 在代码说明中使用

```bash
# 启动服务
./start.sh

# ==重要==：确保端口8080未被占用
netstat -an | grep 8080
```

## 注意事项

### 语法冲突

1. **删除线语法**：避免使用 `~~文本~~` 作为高亮，因为这是Markdown的删除线语法
2. **标题语法**：使用 `##文本##` 时确保前后有空格，避免与标题混淆
3. **代码语法**：在代码块中高亮语法不会被处理

### 移动端适配

高亮文本在移动端会自动调整：
- 字体大小适配
- 触摸友好的间距
- 响应式布局

### 深色模式

所有高亮颜色都支持深色模式，会自动调整为适合深色背景的颜色。

## 技术实现

高亮文本功能通过以下技术实现：

1. **CSS样式**：定义各种颜色的高亮样式
2. **JavaScript插件**：解析Markdown语法并转换为HTML
3. **响应式设计**：适配不同设备和主题

想了解更多文档编写技巧，请查看[使用手册](user-guide.md)。
