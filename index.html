<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>一花CCPROXY系统商业版项目文档</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="description" content="一花CCP项目的完整文档，包含安装、使用、开发等指南">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
  <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/docsify@4/lib/themes/vue.css">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
  <div id="app"></div>
  <script>
    window.$docsify = {
      name: '一花CCPROXY系统商业版项目文档',
      repo: '',
      loadSidebar: true,
      subMaxLevel: 2,
      auto2top: true,
      coverpage: true,
      // 移动端优化配置
      themeColor: '#3fcfbb',
      maxLevel: 4,
      loadNavbar: false,
      mergeNavbar: true,
      executeScript: true,
      noEmoji: false,
      // 搜索配置
      search: {
        maxAge: 86400000,
        paths: 'auto',
        placeholder: '搜索文档...',
        noData: '没有找到结果',
        depth: 3,
        hideOtherSidebarContent: false
      },
      // 分页配置
      pagination: {
        previousText: '上一页',
        nextText: '下一页',
        crossChapter: true,
        crossChapterText: true
      },
      // 代码复制配置
      copyCode: {
        buttonText: '复制',
        errorText: '复制失败',
        successText: '已复制'
      },
      // 移动端侧边栏配置
      hideSidebar: false,
      // 字数统计配置
      count: {
        countable: true,
        fontsize: '0.9em',
        color: 'rgb(90,90,90)',
        language: 'chinese'
      }
    }
  </script>
  <!-- Docsify v4 -->
  <script src="//cdn.jsdelivr.net/npm/docsify@4"></script>
  <!-- 搜索插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify/lib/plugins/search.min.js"></script>
  <!-- 分页插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify-pagination/dist/docsify-pagination.min.js"></script>
  <!-- 代码复制插件 -->
  <script src="//cdn.jsdelivr.net/npm/docsify-copy-code/dist/docsify-copy-code.min.js"></script>
  <!-- 高亮文本插件 -->
  <script src="//cdn.jsdelivr.net/npm/prismjs@1/components/prism-markdown.min.js"></script>

  <!-- 自定义高亮文本处理脚本 -->
  <script>
    // 高亮文本处理插件
    window.$docsify.plugins = [].concat(function(hook, vm) {
      hook.beforeEach(function(content) {
        // 处理 ==文本== 语法
        content = content.replace(/==([^=]+)==\{\.([^}]+)\}/g, '<mark class="$2">$1</mark>');
        content = content.replace(/==([^=]+)==/g, '<mark>$1</mark>');

        // 处理 !!文本!! 语法（红色高亮）
        content = content.replace(/!!([^!]+)!!/g, '<mark class="red">$1</mark>');

        // 处理 @@文本@@ 语法（青绿色高亮）
        content = content.replace(/@@([^@]+)@@/g, '<mark class="cyan">$1</mark>');

        // 处理 ##文本## 语法（绿色高亮）
        content = content.replace(/##([^#]+)##(?!\s*{)/g, '<mark class="green">$1</mark>');

        // 处理 %%文本%% 语法（蓝色高亮）
        content = content.replace(/%%([^%]+)%%/g, '<mark class="blue">$1</mark>');

        // 处理 ^^文本^^ 语法（紫色高亮）
        content = content.replace(/\^\^([^^]+)\^\^/g, '<mark class="purple">$1</mark>');

        // 处理 ~~文本~~ 语法（橙色高亮，避免与删除线冲突）
        content = content.replace(/~\~([^~]+)~\~/g, '<mark class="orange">$1</mark>');

        return content;
      });
    }, window.$docsify.plugins || []);
  </script>

  <!-- 深色模式切换脚本 -->
  <script>
    // 检测系统偏好并应用
    function applyTheme() {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const savedTheme = localStorage.getItem('theme');

      if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        document.body.classList.add('dark-mode');
      } else {
        document.body.classList.remove('dark-mode');
      }
    }

    // 页面加载时应用主题
    applyTheme();

    // 监听系统主题变化
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', applyTheme);

    // 提供手动切换功能（可选）
    window.toggleTheme = function() {
      const isDark = document.body.classList.contains('dark-mode');
      if (isDark) {
        document.body.classList.remove('dark-mode');
        localStorage.setItem('theme', 'light');
      } else {
        document.body.classList.add('dark-mode');
        localStorage.setItem('theme', 'dark');
      }
    };
  </script>
</body>
</html>
