<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect x="5" y="5" width="190" height="70" rx="15" ry="15" fill="url(#logoGradient)" stroke="#1B5E20" stroke-width="2"/>
  
  <!-- 图标部分 - 聊天气泡 -->
  <g transform="translate(20, 20)">
    <!-- 主聊天气泡 -->
    <ellipse cx="15" cy="15" rx="12" ry="10" fill="white" opacity="0.9"/>
    <!-- 小聊天气泡 -->
    <ellipse cx="25" cy="25" rx="8" ry="6" fill="white" opacity="0.7"/>
    <!-- 连接点 -->
    <circle cx="20" cy="20" r="2" fill="white"/>
    
    <!-- 消息线条 -->
    <line x1="10" y1="12" x2="20" y2="12" stroke="#4CAF50" stroke-width="2" stroke-linecap="round"/>
    <line x1="10" y1="15" x2="18" y2="15" stroke="#4CAF50" stroke-width="2" stroke-linecap="round"/>
    <line x1="10" y1="18" x2="16" y2="18" stroke="#4CAF50" stroke-width="2" stroke-linecap="round"/>
  </g>
  
  <!-- 文字部分 -->
  <text x="70" y="35" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white">一花CCP</text>
  <text x="70" y="55" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)">Customer Communication Platform</text>
</svg>
