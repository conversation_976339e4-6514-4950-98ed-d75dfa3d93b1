/* 震撼3D青绿色系封面设计 */
.cover {
  background:
    radial-gradient(circle at 30% 20%, rgba(63,207,187,0.8) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(51,202,187,0.6) 0%, transparent 50%),
    linear-gradient(135deg, #2aa698 0%, #3fcfbb 50%, #33cabb 100%) !important;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  perspective: 1200px;
}

/* 优化的背景装饰元素 */
.cover::before {
  content: '';
  position: absolute;
  top: 20%;
  right: -5%;
  width: 80px;
  height: 80px;
  background: linear-gradient(145deg, rgba(248,249,250,0.3) 0%, rgba(240,249,252,0.2) 100%);
  border-radius: 50%;
  animation: gentle3DFloat 20s ease-in-out infinite;
  box-shadow: 0 8px 24px rgba(63,207,187,0.05);
  transform: perspective(400px) rotateX(8deg);
  z-index: 0;
  opacity: 0.6;
}

.cover::after {
  content: '';
  position: absolute;
  bottom: 25%;
  left: -3%;
  width: 60px;
  height: 60px;
  background: linear-gradient(145deg, rgba(240,249,252,0.2) 0%, rgba(212,237,218,0.15) 100%);
  border-radius: 50%;
  animation: gentle3DFloat 25s ease-in-out infinite reverse;
  box-shadow: 0 6px 18px rgba(42,166,152,0.04);
  transform: perspective(300px) rotateX(-6deg);
  z-index: 0;
  opacity: 0.5;
}

@keyframes gentle3DFloat {
  0%, 100% {
    transform: perspective(800px) rotateX(15deg) rotateY(-10deg) translateY(0px);
    box-shadow:
      0 20px 40px rgba(63,207,187,0.15),
      0 8px 16px rgba(0,0,0,0.08),
      inset 0 1px 0 rgba(255,255,255,0.8),
      inset 0 -1px 0 rgba(63,207,187,0.1);
  }
  25% {
    transform: perspective(800px) rotateX(18deg) rotateY(-8deg) translateY(-6px);
    box-shadow:
      0 24px 48px rgba(63,207,187,0.18),
      0 10px 20px rgba(0,0,0,0.1),
      inset 0 1px 0 rgba(255,255,255,0.9),
      inset 0 -1px 0 rgba(63,207,187,0.12);
  }
  50% {
    transform: perspective(800px) rotateX(12deg) rotateY(-12deg) translateY(-10px);
    box-shadow:
      0 28px 56px rgba(63,207,187,0.2),
      0 12px 24px rgba(0,0,0,0.12),
      inset 0 1px 0 rgba(255,255,255,1),
      inset 0 -1px 0 rgba(63,207,187,0.15);
  }
  75% {
    transform: perspective(800px) rotateX(16deg) rotateY(-9deg) translateY(-6px);
    box-shadow:
      0 24px 48px rgba(63,207,187,0.18),
      0 10px 20px rgba(0,0,0,0.1),
      inset 0 1px 0 rgba(255,255,255,0.9),
      inset 0 -1px 0 rgba(63,207,187,0.12);
  }
}

.cover-main {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  padding: 4rem 2rem;
  animation: fadeInUp 1.2s ease-out;
  transform-style: preserve-3d;
}

/* 震撼3D标题效果 */
.cover-main h1 {
  font-size: 5.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
  color: #ffffff;
  letter-spacing: 0.05em;
  text-shadow:
    0 1px 0 #33cabb,
    0 2px 0 #2aa698,
    0 3px 0 #258a7a,
    0 4px 0 #1f7a6b,
    0 5px 0 #1a6a5c,
    0 6px 0 #155a4d,
    0 8px 8px rgba(0,0,0,0.3),
    0 12px 20px rgba(42,166,152,0.4),
    0 0 30px rgba(63,207,187,0.6);
  transform: translateZ(80px) rotateX(10deg);
  animation: title3D 4s ease-in-out infinite;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 震撼副标题效果 */
.cover-main h2 {
  font-size: 1.8rem;
  font-weight: 400;
  margin-bottom: 1.5rem;
  color: #ffffff;
  letter-spacing: 0.03em;
  text-shadow:
    0 2px 4px rgba(0,0,0,0.4),
    0 4px 8px rgba(42,166,152,0.3),
    0 0 20px rgba(63,207,187,0.4);
  transform: translateZ(40px) rotateX(5deg);
  animation: slideInLeft 1s ease-out 0.3s both;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 引言文字简化 */
.cover-main blockquote {
  font-size: 1.3rem;
  font-style: italic;
  color: rgba(255,255,255,0.95);
  border-left: 3px solid rgba(255,255,255,0.6);
  padding-left: 1.2rem;
  margin: 2rem auto;
  max-width: 450px;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  animation: slideInRight 1s ease-out 0.6s both;
  background: rgba(255,255,255,0.1);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  font-weight: 300;
}

@keyframes title3D {
  0%, 100% {
    transform: translateZ(50px) rotateX(0deg);
  }
  50% {
    transform: translateZ(60px) rotateX(5deg);
  }
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 特性标题简化 */
.cover-main p strong {
  display: block;
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0,0,0,0.4);
  animation: slideInUp 1s ease-out 0.9s both;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.cover-main ul {
  text-align: left;
  display: inline-block;
  margin: 2rem 0;
  padding: 0;
  list-style: none;
  max-width: 650px;
  perspective: 1000px;
}

/* 简约3D特性列表 */
.cover-main ul li {
  font-size: 1.1rem;
  margin: 1.2rem 0;
  color: #ffffff;
  line-height: 1.6;
  padding: 1.1rem 1.8rem;
  background: rgba(255,255,255,0.15);
  border-radius: 12px;
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
  animation: slideInUp 1s ease-out both;
  box-shadow:
    0 8px 16px rgba(0,0,0,0.15),
    0 3px 6px rgba(42,166,152,0.2),
    inset 0 1px 0 rgba(255,255,255,0.4),
    inset 0 -1px 0 rgba(63,207,187,0.1);
  position: relative;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400;
  transform: perspective(400px) rotateX(3deg) translateZ(10px);
}

.cover-main ul li::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(63,207,187,0.15), rgba(51,202,187,0.1));
  border-radius: 15px;
  z-index: -1;
}

.cover-main ul li:nth-child(1) {
  animation-delay: 1.2s;
  border-left-color: #3fcfbb;
  transform: translateZ(15px) rotateY(-2deg);
}
.cover-main ul li:nth-child(2) {
  animation-delay: 1.4s;
  border-left-color: #33cabb;
  transform: translateZ(20px) rotateY(1deg);
}
.cover-main ul li:nth-child(3) {
  animation-delay: 1.6s;
  border-left-color: #2aa698;
  transform: translateZ(18px) rotateY(-1deg);
}
.cover-main ul li:nth-child(4) {
  animation-delay: 1.8s;
  border-left-color: #3fcfbb;
  transform: translateZ(22px) rotateY(2deg);
}
.cover-main ul li:nth-child(5) {
  animation-delay: 2.0s;
  border-left-color: #33cabb;
  transform: translateZ(16px) rotateY(-1.5deg);
}
.cover-main ul li:nth-child(6) {
  animation-delay: 2.2s;
  border-left-color: #2aa698;
  transform: translateZ(19px) rotateY(1.5deg);
}

/* 特性列表项悬停效果优化 */
.cover-main ul li:hover {
  background: rgba(63,207,187,0.25);
  transform: perspective(400px) rotateX(1deg) translateZ(15px) translateY(-2px);
  box-shadow:
    0 12px 24px rgba(42,166,152,0.3),
    0 4px 8px rgba(0,0,0,0.2),
    inset 0 1px 0 rgba(255,255,255,0.6),
    inset 0 -1px 0 rgba(63,207,187,0.15);
  text-shadow: 0 1px 2px rgba(0,0,0,0.4);
}

/* emoji和文字对齐优化 */
.cover-main ul li strong {
  display: inline-block;
  margin-right: 0.5rem;
  vertical-align: middle;
}

.cover-main ul li::before {
  content: '';
  display: inline-block;
  width: 0.5rem;
  vertical-align: middle;
}

/* 简约3D按钮效果 */
.cover-main p:last-child {
  animation: slideInUp 1s ease-out 2.5s both;
  perspective: 1000px;
}

.cover-main p:last-child a {
  display: inline-block;
  margin: 1rem 1.5rem;
  padding: 1.2rem 2.8rem;
  text-decoration: none;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
  transform: perspective(600px) rotateX(8deg) translateZ(20px);
  box-shadow:
    0 12px 24px rgba(63,207,187,0.2),
    0 4px 8px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.6),
    inset 0 -1px 0 rgba(63,207,187,0.1);
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  letter-spacing: 0.02em;
  text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.cover-main p:last-child a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 15px;
}

.cover-main p:last-child a:hover::before {
  opacity: 1;
}

/* 第一个按钮 - 震撼主要按钮 */
.cover-main p:last-child a:nth-child(1) {
  background: linear-gradient(135deg, #3fcfbb 0%, #33cabb 50%, #2aa698 100%);
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.cover-main p:last-child a:nth-child(1):hover {
  background: linear-gradient(135deg, #4dd4c7 0%, #3fcfbb 50%, #33cabb 100%);
  transform: perspective(600px) rotateX(4deg) translateZ(30px) translateY(-2px);
  box-shadow:
    0 16px 32px rgba(63, 207, 187, 0.25),
    0 6px 12px rgba(0,0,0,0.12),
    inset 0 1px 0 rgba(255,255,255,0.8),
    inset 0 -1px 0 rgba(63,207,187,0.15);
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* 第二个按钮 - 震撼次要按钮 */
.cover-main p:last-child a:nth-child(2) {
  background: rgba(255,255,255,0.15);
  color: #ffffff;
  border: 3px solid rgba(255,255,255,0.7);
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.cover-main p:last-child a:nth-child(2):hover {
  background: rgba(255,255,255,0.25);
  border-color: rgba(255,255,255,0.9);
  transform: translateZ(50px) rotateX(-10deg) scale(1.05);
  box-shadow:
    0 25px 60px rgba(255,255,255, 0.3),
    0 15px 30px rgba(255,255,255,0.2),
    inset 0 2px 0 rgba(255,255,255,0.4);
  text-shadow: 0 3px 6px rgba(0,0,0,0.6);
}

/* 第三个按钮 - 震撼GitHub按钮 */
.cover-main p:last-child a:nth-child(3) {
  background: linear-gradient(135deg, #2aa698 0%, #258a7a 50%, #1f7a6b 100%);
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.cover-main p:last-child a:nth-child(3):hover {
  background: linear-gradient(135deg, #33b5a5 0%, #2aa698 50%, #258a7a 100%);
  transform: translateZ(50px) rotateX(-10deg) scale(1.05);
  box-shadow:
    0 25px 60px rgba(42, 166, 152, 0.6),
    0 15px 30px rgba(37,138,122,0.4),
    inset 0 2px 0 rgba(255,255,255,0.4);
  text-shadow: 0 3px 6px rgba(0,0,0,0.6);
}

@keyframes title3D {
  0%, 100% {
    transform: translateZ(80px) rotateX(10deg);
  }
  50% {
    transform: translateZ(90px) rotateX(15deg);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移除复杂的统计和动画样式 */

/* 额外的动态装饰元素 */
.cover-main::before {
  content: '';
  position: absolute;
  top: 15%;
  right: 12%;
  width: 3px;
  height: 3px;
  background: rgba(63,207,187,0.3);
  border-radius: 50%;
  animation: twinkle 6s ease-in-out infinite;
  z-index: 2;
  box-shadow:
    0 0 6px rgba(63,207,187,0.2),
    1px 1px 0 rgba(63,207,187,0.15);
}

.cover-main::after {
  content: '';
  position: absolute;
  bottom: 18%;
  left: 12%;
  width: 2px;
  height: 2px;
  background: rgba(51,202,187,0.25);
  border-radius: 50%;
  animation: bounce 8s ease-in-out infinite;
  z-index: 2;
  box-shadow: 0 0 4px rgba(51,202,187,0.15);
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.3);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0px);
    opacity: 0.15;
  }
  50% {
    transform: translateY(-3px);
    opacity: 0.3;
  }
}

/* 鼠标悬停时的整体效果 */
.cover:hover .cover-main h1 {
  animation-duration: 2s;
}

.cover:hover .cover::before {
  animation-duration: 10s;
}

/* 侧边栏样式优化 - 青绿色系 */
.sidebar {
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.sidebar-nav li a {
  color: #495057;
  font-weight: 500;
  transition: color 0.3s ease;
}

.sidebar-nav li a:hover {
  color: #3fcfbb;
}

.sidebar-nav li.active > a {
  color: #3fcfbb;
  font-weight: 600;
  border-right: 3px solid #3fcfbb;
}

/* 内容区域样式 */
.content {
  padding-top: 60px;
}

/* 搜索框样式 - 青绿色系 */
.search input {
  border: 2px solid #e9ecef;
  border-radius: 25px;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.search input:focus {
  border-color: #3fcfbb;
  outline: none;
  box-shadow: 0 0 0 3px rgba(63, 207, 187, 0.2);
}

/* 代码块样式优化 - 简约3D风格 */
pre[data-lang] {
  background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 50%, #f0f9fc 100%) !important;
  border-radius: 16px;
  box-shadow:
    0 8px 24px rgba(0,0,0,0.08),
    0 3px 8px rgba(63,207,187,0.12),
    inset 0 1px 0 rgba(255,255,255,0.9),
    inset 0 -1px 0 rgba(63,207,187,0.08);
  border: 1px solid rgba(63,207,187,0.15);
  position: relative;
  margin: 1.5rem 0;
  overflow: hidden;
  transform: perspective(600px) rotateX(2deg);
}

/* 代码块头部标签 */
pre[data-lang]::before {
  content: attr(data-lang);
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #3fcfbb 0%, #2aa698 100%);
  color: white;
  padding: 0.3rem 0.8rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom-right-radius: 8px;
  box-shadow: 0 2px 8px rgba(63,207,187,0.3);
  z-index: 10;
}

/* 代码块内容 */
pre[data-lang] code {
  background: transparent !important;
  color: #2d3748 !important;
  font-family: 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.9rem !important;
  line-height: 1.6 !important;
  padding: 1.5rem 1.2rem 1.2rem 1.2rem !important;
  display: block;
  overflow-x: auto;
}

/* 代码块复制按钮优化 */
.docsify-copy-code-button {
  background: linear-gradient(135deg, #3fcfbb 0%, #2aa698 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.4rem 0.8rem !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(63,207,187,0.3) !important;
  top: 0.8rem !important;
  right: 0.8rem !important;
  z-index: 20 !important;
}

.docsify-copy-code-button:hover {
  background: linear-gradient(135deg, #4dd4c7 0%, #3fcfbb 100%) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(63,207,187,0.4) !important;
}

/* 代码块语法高亮优化 - 日间模式 */
pre[data-lang] .token.comment {
  color: #6a737d !important;
  font-style: italic;
}

pre[data-lang] .token.string {
  color: #2aa698 !important;
  font-weight: 500;
}

pre[data-lang] .token.keyword {
  color: #d73a49 !important;
  font-weight: 600;
}

pre[data-lang] .token.function {
  color: #6f42c1 !important;
  font-weight: 500;
}

pre[data-lang] .token.number {
  color: #e36209 !important;
}

pre[data-lang] .token.operator {
  color: #586069 !important;
}

pre[data-lang] .token.punctuation {
  color: #586069 !important;
}

pre[data-lang] .token.variable {
  color: #005cc5 !important;
}

pre[data-lang] .token.property {
  color: #2aa698 !important;
}

/* 行号样式（如果启用） */
pre[data-lang] .line-numbers-rows {
  border-right: 1px solid rgba(63,207,187,0.3) !important;
  background: rgba(63,207,187,0.05) !important;
}

pre[data-lang] .line-numbers-rows > span:before {
  color: rgba(63,207,187,0.6) !important;
  font-size: 0.8rem !important;
}

/* 特殊代码块类型样式 */
/* Bash/Shell 命令样式 */
pre[data-lang="bash"]::before,
pre[data-lang="shell"]::before,
pre[data-lang="sh"]::before {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
  content: "TERMINAL" !important;
  left: 0 !important;
  border-bottom-right-radius: 8px !important;
}

pre[data-lang="bash"],
pre[data-lang="shell"],
pre[data-lang="sh"] {
  background: linear-gradient(135deg, #f1f8e9 0%, #f8f9fa 100%) !important;
  border-left: 4px solid #4caf50;
}

/* YAML/配置文件样式 */
pre[data-lang="yaml"]::before,
pre[data-lang="yml"]::before {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
  content: "CONFIG" !important;
  left: 0 !important;
  border-bottom-right-radius: 8px !important;
}

pre[data-lang="yaml"],
pre[data-lang="yml"] {
  border-left: 4px solid #ff9800;
}

/* JSON 样式 */
pre[data-lang="json"]::before {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%) !important;
  content: "JSON" !important;
  left: 0 !important;
  border-bottom-right-radius: 8px !important;
}

pre[data-lang="json"] {
  border-left: 4px solid #9c27b0;
}

/* SQL 样式 */
pre[data-lang="sql"]::before {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
  content: "DATABASE" !important;
  left: 0 !important;
  border-bottom-right-radius: 8px !important;
}

pre[data-lang="sql"] {
  border-left: 4px solid #2196f3;
}

/* JavaScript 样式 */
pre[data-lang="javascript"]::before,
pre[data-lang="js"]::before {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
  content: "JAVASCRIPT" !important;
  color: #1a1a1a !important;
  left: 0 !important;
  border-bottom-right-radius: 8px !important;
}

pre[data-lang="javascript"],
pre[data-lang="js"] {
  border-left: 4px solid #ffc107;
}

/* Docker 样式 */
pre[data-lang="dockerfile"]::before,
pre[data-lang="docker"]::before {
  background: linear-gradient(135deg, #2196f3 0%, #0d47a1 100%) !important;
  content: "DOCKER" !important;
  left: 0 !important;
  border-bottom-right-radius: 8px !important;
}

pre[data-lang="dockerfile"],
pre[data-lang="docker"] {
  border-left: 4px solid #2196f3;
}

/* 无语言标识的代码块 */
pre:not([data-lang]) {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
  border-radius: 12px;
  box-shadow:
    0 4px 16px rgba(0,0,0,0.1),
    0 2px 8px rgba(63,207,187,0.1);
  border: 1px solid rgba(63,207,187,0.2);
  border-left: 4px solid #3fcfbb;
  margin: 1.5rem 0;
}

pre:not([data-lang]) code {
  background: transparent !important;
  color: #2d3748 !important;
  font-family: 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 0.9rem !important;
  line-height: 1.6 !important;
  padding: 1.2rem !important;
  display: block;
  overflow-x: auto;
}

/* 表格样式 - 青绿色系 */
table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(42,166,152,0.1);
}

table th {
  background: linear-gradient(135deg, #3fcfbb, #33cabb);
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

table tr:hover {
  background-color: rgba(63,207,187,0.1);
}

/* 文档内容链接样式 */
.content a {
  color: #3fcfbb;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.content a:hover {
  color: #2aa698;
  border-bottom-color: #3fcfbb;
}

/* 内联代码样式优化 */
.content code {
  background: linear-gradient(135deg, rgba(63,207,187,0.15) 0%, rgba(51,202,187,0.1) 100%);
  color: #2aa698;
  padding: 0.25rem 0.6rem;
  border-radius: 6px;
  font-weight: 600;
  font-family: 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(63,207,187,0.2);
  box-shadow: 0 1px 3px rgba(63,207,187,0.1);
  transition: all 0.2s ease;
  position: relative;
}

.content code:hover {
  background: linear-gradient(135deg, rgba(63,207,187,0.2) 0%, rgba(51,202,187,0.15) 100%);
  border-color: rgba(63,207,187,0.3);
  box-shadow: 0 2px 6px rgba(63,207,187,0.2);
  transform: translateY(-1px);
}

/* 代码块中的内联代码不应用悬停效果 */
pre code {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

pre code:hover {
  background: transparent !important;
  transform: none !important;
}

/* 引用块样式 */
.content blockquote {
  border-left: 4px solid #3fcfbb;
  background: rgba(63,207,187,0.05);
  margin: 1rem 0;
  padding: 1rem 1.5rem;
  border-radius: 0 8px 8px 0;
}

.content blockquote p {
  color: #2aa698;
  font-style: italic;
}

/* 响应式设计 - 平板端优化 */
@media (max-width: 768px) {
  .cover-main {
    padding: 2rem 1rem;
  }

  .cover-main h1 {
    font-size: 2.8rem;
  }

  .cover-main h2 {
    font-size: 1.3rem;
  }

  .cover-main ul {
    max-width: 100%;
    padding: 0 1rem;
  }

  .cover-main ul li {
    font-size: 1rem;
    padding: 0.6rem 1rem;
    margin: 0.8rem 0;
  }

  .cover-main p:last-child a {
    display: block;
    margin: 0.8rem auto;
    max-width: 280px;
    padding: 0.8rem 2rem;
  }

  /* 字数统计移动端优化 */
  .cover-main div[style*="display: inline-flex"] {
    flex-direction: column !important;
    gap: 10px !important;
    align-items: center !important;
  }

  .cover-main div[style*="display: inline-flex"] span {
    font-size: 0.8em !important;
    padding: 6px 12px !important;
  }

  /* 章节统计信息移动端优化 */
  div[style*="border-left: 4px solid #3fcfbb"] {
    margin: 15px 0 !important;
    padding: 12px 15px !important;
  }

  div[style*="border-left: 4px solid #3fcfbb"] div {
    flex-direction: column !important;
    gap: 8px !important;
    align-items: flex-start !important;
  }

  div[style*="border-left: 4px solid #3fcfbb"] span {
    font-size: 0.8em !important;
    padding: 4px 10px !important;
  }

  /* 内容区域优化 */
  .content {
    padding: 20px 15px !important;
  }

  /* 侧边栏移动端优化 */
  .sidebar {
    width: 280px !important;
  }

  .sidebar-nav li a {
    padding: 8px 15px !important;
    font-size: 0.9rem !important;
  }

  /* 表格移动端优化 */
  table {
    font-size: 0.85rem !important;
    overflow-x: auto !important;
    display: block !important;
    white-space: nowrap !important;
  }

  /* 减少动画延迟 */
  .cover-main ul li:nth-child(1) { animation-delay: 0.8s; }
  .cover-main ul li:nth-child(2) { animation-delay: 1.0s; }
  .cover-main ul li:nth-child(3) { animation-delay: 1.2s; }
  .cover-main ul li:nth-child(4) { animation-delay: 1.4s; }
  .cover-main ul li:nth-child(5) { animation-delay: 1.6s; }
  .cover-main ul li:nth-child(6) { animation-delay: 1.8s; }
}

/* 手机端优化 */
@media (max-width: 480px) {
  /* 封面优化 */
  .cover-main {
    padding: 1rem 0.8rem;
  }

  .cover-main h1 {
    font-size: 2rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.8rem !important;
  }

  .cover-main h2 {
    font-size: 1rem !important;
    line-height: 1.4 !important;
    margin-bottom: 1rem !important;
  }

  .cover-main blockquote {
    font-size: 1rem !important;
    margin: 1rem auto !important;
    padding: 0.8rem 1rem !important;
    max-width: 90% !important;
  }

  .cover-main ul li {
    font-size: 0.9rem !important;
    padding: 0.6rem 0.8rem !important;
    margin: 0.6rem 0 !important;
    line-height: 1.4 !important;
  }

  .cover-main p:last-child a {
    padding: 0.8rem 1.2rem !important;
    font-size: 0.9rem !important;
    margin: 0.6rem auto !important;
    max-width: 200px !important;
  }

  /* 封面统计信息手机端优化 */
  .cover-main div[style*="display: inline-flex"] {
    flex-direction: column !important;
    gap: 8px !important;
    margin: 20px 0 !important;
  }

  .cover-main div[style*="display: inline-flex"] span {
    font-size: 0.75em !important;
    padding: 5px 10px !important;
    border-radius: 15px !important;
  }

  /* 章节统计信息手机端优化 */
  div[style*="border-left: 4px solid #3fcfbb"] {
    margin: 10px 0 !important;
    padding: 10px 12px !important;
    border-radius: 6px !important;
  }

  div[style*="border-left: 4px solid #3fcfbb"] div {
    flex-direction: column !important;
    gap: 6px !important;
    align-items: flex-start !important;
  }

  div[style*="border-left: 4px solid #3fcfbb"] span {
    font-size: 0.75em !important;
    padding: 3px 8px !important;
    border-radius: 10px !important;
  }

  /* 内容区域手机端优化 */
  .content {
    padding: 15px 12px !important;
    font-size: 0.95rem !important;
    line-height: 1.6 !important;
  }

  .content h1 {
    font-size: 1.8rem !important;
    margin: 1.5rem 0 1rem 0 !important;
  }

  .content h2 {
    font-size: 1.4rem !important;
    margin: 1.2rem 0 0.8rem 0 !important;
  }

  .content h3 {
    font-size: 1.2rem !important;
    margin: 1rem 0 0.6rem 0 !important;
  }

  .content p {
    margin: 0.8rem 0 !important;
  }

  .content ul, .content ol {
    padding-left: 1.2rem !important;
    margin: 0.8rem 0 !important;
  }

  .content li {
    margin: 0.4rem 0 !important;
    line-height: 1.5 !important;
  }

  /* 侧边栏手机端优化 */
  .sidebar {
    width: 260px !important;
    font-size: 0.9rem !important;
  }

  .sidebar-nav li a {
    padding: 10px 12px !important;
    font-size: 0.85rem !important;
    line-height: 1.4 !important;
  }

  /* 搜索框手机端优化 */
  .search input {
    font-size: 0.9rem !important;
    padding: 0.6rem 1rem !important;
  }

  /* 表格手机端优化 */
  table {
    font-size: 0.8rem !important;
    margin: 1rem 0 !important;
  }

  table th, table td {
    padding: 0.5rem 0.3rem !important;
  }

  /* 引用块手机端优化 */
  .content blockquote {
    margin: 0.8rem 0 !important;
    padding: 0.8rem 1rem !important;
    font-size: 0.9rem !important;
  }

  /* 移动端装饰元素优化 */
  .cover::before {
    width: 40px;
    height: 40px;
    top: 8%;
    right: 3%;
    opacity: 0.4;
    animation-duration: 25s;
  }

  .cover::after {
    width: 30px;
    height: 30px;
    bottom: 12%;
    left: 3%;
    opacity: 0.3;
    animation-duration: 30s;
  }

  .cover-main::before,
  .cover-main::after {
    display: none;
  }

  /* 移动端代码块优化 */
  pre[data-lang] {
    margin: 0.8rem 0 !important;
    border-radius: 8px !important;
  }

  pre[data-lang] code {
    font-size: 0.75rem !important;
    padding: 0.8rem 0.6rem !important;
    line-height: 1.4 !important;
  }

  pre[data-lang]::before {
    font-size: 0.65rem !important;
    padding: 0.2rem 0.5rem !important;
  }

  .docsify-copy-code-button {
    font-size: 0.65rem !important;
    padding: 0.25rem 0.5rem !important;
    top: 0.5rem !important;
    right: 0.5rem !important;
  }

  .content code {
    font-size: 0.8em !important;
    padding: 0.2rem 0.4rem !important;
  }

  /* 移动端触摸优化 */
  .content a {
    min-height: 44px !important;
    display: inline-block !important;
    line-height: 1.4 !important;
  }

  /* 移动端按钮优化 */
  button, .btn {
    min-height: 44px !important;
    padding: 0.6rem 1rem !important;
    font-size: 0.9rem !important;
  }
}

/* 代码块滚动条样式 */
pre[data-lang] code::-webkit-scrollbar {
  height: 8px;
}

pre[data-lang] code::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
  border-radius: 4px;
}

pre[data-lang] code::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3fcfbb 0%, #2aa698 100%);
  border-radius: 4px;
}

pre[data-lang] code::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4dd4c7 0%, #3fcfbb 100%);
}

/* 代码块加载动画 */
@keyframes codeBlockFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

pre[data-lang] {
  animation: codeBlockFadeIn 0.5s ease-out;
}

/* 代码块聚焦效果 */
pre[data-lang]:hover {
  box-shadow:
    0 12px 32px rgba(0,0,0,0.12),
    0 4px 12px rgba(63,207,187,0.18),
    inset 0 1px 0 rgba(255,255,255,1),
    inset 0 -1px 0 rgba(63,207,187,0.12);
  transform: perspective(600px) rotateX(1deg) translateY(-3px);
  transition: all 0.3s ease;
}

/* 超小屏幕设备优化 (iPhone SE等) */
@media (max-width: 375px) {
  .cover-main h1 {
    font-size: 1.8rem !important;
  }

  .cover-main h2 {
    font-size: 0.95rem !important;
  }

  .cover-main blockquote {
    font-size: 0.9rem !important;
    padding: 0.6rem 0.8rem !important;
  }

  .cover-main ul li {
    font-size: 0.85rem !important;
    padding: 0.5rem 0.6rem !important;
  }

  .cover-main p:last-child a {
    padding: 0.6rem 1rem !important;
    font-size: 0.85rem !important;
    max-width: 180px !important;
  }

  .content {
    padding: 12px 10px !important;
    font-size: 0.9rem !important;
  }

  .content h1 {
    font-size: 1.6rem !important;
  }

  .content h2 {
    font-size: 1.3rem !important;
  }

  .content h3 {
    font-size: 1.1rem !important;
  }

  pre[data-lang] code {
    font-size: 0.7rem !important;
    padding: 0.6rem 0.5rem !important;
  }

  .sidebar {
    width: 240px !important;
  }
}

/* 横屏模式优化 */
@media (max-height: 500px) and (orientation: landscape) {
  .cover-main {
    padding: 1rem 0.5rem !important;
  }

  .cover-main h1 {
    font-size: 1.8rem !important;
    margin-bottom: 0.5rem !important;
  }

  .cover-main h2 {
    font-size: 1rem !important;
    margin-bottom: 0.5rem !important;
  }

  .cover-main blockquote {
    margin: 0.8rem auto !important;
    font-size: 0.9rem !important;
  }

  .cover-main ul li {
    margin: 0.4rem 0 !important;
    padding: 0.4rem 0.6rem !important;
  }

  .cover-main p:last-child a {
    margin: 0.4rem auto !important;
    padding: 0.5rem 1rem !important;
  }

  /* 横屏模式下隐藏装饰元素 */
  .cover::before,
  .cover::after,
  .cover-main::before,
  .cover-main::after {
    display: none !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除悬停效果，优化触摸体验 */
  .cover-main ul li:hover,
  .cover-main p:last-child a:hover,
  pre[data-lang]:hover {
    transform: none !important;
  }

  /* 增大触摸目标 */
  .content a {
    padding: 0.2rem 0 !important;
    min-height: 44px !important;
  }

  .docsify-copy-code-button {
    min-width: 44px !important;
    min-height: 44px !important;
  }

  /* 优化滚动性能 */
  .content {
    -webkit-overflow-scrolling: touch !important;
  }

  pre[data-lang] code {
    -webkit-overflow-scrolling: touch !important;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .cover-main h1 {
    text-rendering: optimizeLegibility !important;
  }

  .content {
    text-rendering: optimizeSpeed !important;
  }

  pre[data-lang] code {
    text-rendering: optimizeSpeed !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .content {
    background-color: #1a1a1a !important;
    color: #e6e6e6 !important;
  }

  .sidebar {
    background-color: #2a2a2a !important;
    border-right-color: #404040 !important;
  }

  .sidebar-nav li a {
    color: #cccccc !important;
  }

  .sidebar-nav li a:hover {
    color: #3fcfbb !important;
  }

  /* 深色模式下的代码块 */
  pre[data-lang] {
    background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%) !important;
    box-shadow:
      0 8px 32px rgba(0,0,0,0.3),
      0 4px 16px rgba(63,207,187,0.1),
      inset 0 1px 0 rgba(255,255,255,0.1) !important;
  }

  pre[data-lang] code {
    color: #e6e6e6 !important;
  }

  pre:not([data-lang]) {
    background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%) !important;
    box-shadow:
      0 8px 32px rgba(0,0,0,0.3),
      0 4px 16px rgba(63,207,187,0.1) !important;
  }

  pre:not([data-lang]) code {
    color: #e6e6e6 !important;
  }

  /* 深色模式下的语法高亮 */
  pre[data-lang] .token.comment {
    color: #7c7c7c !important;
  }

  pre[data-lang] .token.string {
    color: #3fcfbb !important;
  }

  pre[data-lang] .token.keyword {
    color: #ff6b6b !important;
  }

  pre[data-lang] .token.function {
    color: #4ecdc4 !important;
  }

  pre[data-lang] .token.number {
    color: #ffa726 !important;
  }

  pre[data-lang] .token.operator {
    color: #e6e6e6 !important;
  }

  pre[data-lang] .token.punctuation {
    color: #cccccc !important;
  }

  pre[data-lang] .token.variable {
    color: #a8e6cf !important;
  }

  pre[data-lang] .token.property {
    color: #88d8c0 !important;
  }

  /* 深色模式下的特殊语言类型 */
  pre[data-lang="bash"],
  pre[data-lang="shell"],
  pre[data-lang="sh"] {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
  }
}

/* 手动深色模式支持 */
body.dark-mode .content {
  background-color: #1a1a1a !important;
  color: #e6e6e6 !important;
}

body.dark-mode .sidebar {
  background-color: #2a2a2a !important;
  border-right-color: #404040 !important;
}

body.dark-mode .sidebar-nav li a {
  color: #cccccc !important;
}

body.dark-mode .sidebar-nav li a:hover {
  color: #3fcfbb !important;
}

body.dark-mode pre[data-lang] {
  background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%) !important;
  box-shadow:
    0 8px 32px rgba(0,0,0,0.3),
    0 4px 16px rgba(63,207,187,0.1),
    inset 0 1px 0 rgba(255,255,255,0.1) !important;
}

body.dark-mode pre[data-lang] code {
  color: #e6e6e6 !important;
}

body.dark-mode pre:not([data-lang]) {
  background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%) !important;
  box-shadow:
    0 8px 32px rgba(0,0,0,0.3),
    0 4px 16px rgba(63,207,187,0.1) !important;
}

body.dark-mode pre:not([data-lang]) code {
  color: #e6e6e6 !important;
}

/* 手动深色模式下的语法高亮 */
body.dark-mode pre[data-lang] .token.comment {
  color: #7c7c7c !important;
}

body.dark-mode pre[data-lang] .token.string {
  color: #3fcfbb !important;
}

body.dark-mode pre[data-lang] .token.keyword {
  color: #ff6b6b !important;
}

body.dark-mode pre[data-lang] .token.function {
  color: #4ecdc4 !important;
}

body.dark-mode pre[data-lang] .token.number {
  color: #ffa726 !important;
}

body.dark-mode pre[data-lang] .token.operator {
  color: #e6e6e6 !important;
}

body.dark-mode pre[data-lang] .token.punctuation {
  color: #cccccc !important;
}

body.dark-mode pre[data-lang] .token.variable {
  color: #a8e6cf !important;
}

body.dark-mode pre[data-lang] .token.property {
  color: #88d8c0 !important;
}

body.dark-mode pre[data-lang="bash"],
body.dark-mode pre[data-lang="shell"],
body.dark-mode pre[data-lang="sh"] {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
}

/* Docsify移动端特定优化 */
@media (max-width: 768px) {
  /* 移动端侧边栏优化 */
  .sidebar-toggle {
    background-color: #3fcfbb !important;
    border: none !important;
    width: 44px !important;
    height: 44px !important;
    border-radius: 6px !important;
    box-shadow: 0 2px 8px rgba(63,207,187,0.3) !important;
  }

  .sidebar-toggle span {
    background-color: white !important;
    height: 3px !important;
    border-radius: 2px !important;
  }

  /* 移动端内容区域调整 */
  .content {
    left: 0 !important;
    max-width: none !important;
    padding: 20px 15px 60px 15px !important;
  }

  /* 移动端搜索框优化 */
  .search {
    margin-bottom: 15px !important;
  }

  .search .input-wrap {
    width: 100% !important;
  }

  .search input {
    width: 100% !important;
    box-sizing: border-box !important;
    font-size: 16px !important; /* 防止iOS缩放 */
  }

  /* 移动端分页按钮优化 */
  .pagination-item {
    padding: 12px 20px !important;
    font-size: 0.9rem !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .pagination-item-title {
    font-size: 0.85rem !important;
    line-height: 1.3 !important;
  }

  /* 移动端目录优化 */
  .app-nav {
    margin: 0 !important;
    padding: 10px 15px !important;
  }

  .app-nav li a {
    padding: 8px 12px !important;
    font-size: 0.9rem !important;
  }
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
  body {
    overflow-x: hidden !important;
  }

  .sidebar {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .content {
    overflow-x: hidden !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  /* 防止代码块水平溢出 */
  pre {
    overflow-x: auto !important;
    max-width: 100% !important;
  }

  pre code {
    white-space: pre !important;
    word-wrap: normal !important;
  }

  /* 防止表格溢出 */
  .content table {
    display: block !important;
    overflow-x: auto !important;
    white-space: nowrap !important;
    max-width: 100% !important;
  }

  /* 防止长链接溢出 */
  .content a {
    word-break: break-all !important;
    overflow-wrap: break-word !important;
  }
}

/* iOS Safari特定优化 */
@supports (-webkit-touch-callout: none) {
  .content {
    -webkit-text-size-adjust: 100% !important;
  }

  .search input {
    -webkit-appearance: none !important;
    border-radius: 25px !important;
  }

  .docsify-copy-code-button {
    -webkit-tap-highlight-color: transparent !important;
  }
}

/* Android Chrome特定优化 */
@media screen and (-webkit-min-device-pixel-ratio: 0) and (min-resolution: .001dpcm) {
  .content {
    text-rendering: optimizeSpeed !important;
  }

  pre[data-lang] code {
    font-feature-settings: "liga" 0 !important;
  }
}

/* ==================== 高亮文本样式 ==================== */

/* 基础高亮样式 - 使用 ==文本== 语法 */
mark, .highlight {
  background: linear-gradient(135deg, rgba(255, 235, 59, 0.8) 0%, rgba(255, 193, 7, 0.6) 100%);
  color: #1a1a1a;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(255, 193, 7, 0.3);
  border: 1px solid rgba(255, 193, 7, 0.4);
  transition: all 0.2s ease;
  position: relative;
  display: inline-block;
}

mark:hover, .highlight:hover {
  background: linear-gradient(135deg, rgba(255, 235, 59, 0.9) 0%, rgba(255, 193, 7, 0.7) 100%);
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
  transform: translateY(-1px);
}

/* 青绿色高亮 - 使用 ==文本=={.cyan} 语法 */
mark.cyan, .highlight-cyan {
  background: linear-gradient(135deg, rgba(63, 207, 187, 0.3) 0%, rgba(51, 202, 187, 0.2) 100%);
  color: #2aa698;
  border: 1px solid rgba(63, 207, 187, 0.4);
  box-shadow: 0 1px 3px rgba(63, 207, 187, 0.2);
}

mark.cyan:hover, .highlight-cyan:hover {
  background: linear-gradient(135deg, rgba(63, 207, 187, 0.4) 0%, rgba(51, 202, 187, 0.3) 100%);
  box-shadow: 0 2px 6px rgba(63, 207, 187, 0.3);
}

/* 红色高亮 - 重要提示 */
mark.red, .highlight-red {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.2) 0%, rgba(211, 47, 47, 0.15) 100%);
  color: #c62828;
  border: 1px solid rgba(244, 67, 54, 0.3);
  box-shadow: 0 1px 3px rgba(244, 67, 54, 0.2);
}

mark.red:hover, .highlight-red:hover {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.3) 0%, rgba(211, 47, 47, 0.2) 100%);
  box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

/* 绿色高亮 - 成功提示 */
mark.green, .highlight-green {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(56, 142, 60, 0.15) 100%);
  color: #2e7d32;
  border: 1px solid rgba(76, 175, 80, 0.3);
  box-shadow: 0 1px 3px rgba(76, 175, 80, 0.2);
}

mark.green:hover, .highlight-green:hover {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3) 0%, rgba(56, 142, 60, 0.2) 100%);
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

/* 蓝色高亮 - 信息提示 */
mark.blue, .highlight-blue {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(25, 118, 210, 0.15) 100%);
  color: #1565c0;
  border: 1px solid rgba(33, 150, 243, 0.3);
  box-shadow: 0 1px 3px rgba(33, 150, 243, 0.2);
}

mark.blue:hover, .highlight-blue:hover {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.3) 0%, rgba(25, 118, 210, 0.2) 100%);
  box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
}

/* 紫色高亮 - 特殊标记 */
mark.purple, .highlight-purple {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.2) 0%, rgba(123, 31, 162, 0.15) 100%);
  color: #7b1fa2;
  border: 1px solid rgba(156, 39, 176, 0.3);
  box-shadow: 0 1px 3px rgba(156, 39, 176, 0.2);
}

mark.purple:hover, .highlight-purple:hover {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3) 0%, rgba(123, 31, 162, 0.2) 100%);
  box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
}

/* 橙色高亮 - 警告提示 */
mark.orange, .highlight-orange {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.2) 0%, rgba(245, 124, 0, 0.15) 100%);
  color: #ef6c00;
  border: 1px solid rgba(255, 152, 0, 0.3);
  box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
}

mark.orange:hover, .highlight-orange:hover {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.3) 0%, rgba(245, 124, 0, 0.2) 100%);
  box-shadow: 0 2px 6px rgba(255, 152, 0, 0.3);
}

/* 灰色高亮 - 次要信息 */
mark.gray, .highlight-gray {
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.2) 0%, rgba(117, 117, 117, 0.15) 100%);
  color: #424242;
  border: 1px solid rgba(158, 158, 158, 0.3);
  box-shadow: 0 1px 3px rgba(158, 158, 158, 0.2);
}

mark.gray:hover, .highlight-gray:hover {
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.3) 0%, rgba(117, 117, 117, 0.2) 100%);
  box-shadow: 0 2px 6px rgba(158, 158, 158, 0.3);
}

/* 高亮文本动画效果 */
@keyframes highlightPulse {
  0%, 100% {
    box-shadow: 0 1px 3px rgba(255, 193, 7, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.5);
  }
}

/* 特殊高亮效果 - 闪烁高亮 */
mark.pulse, .highlight-pulse {
  animation: highlightPulse 2s ease-in-out infinite;
}

/* 高亮文本在代码块中的样式 */
pre mark, pre .highlight {
  background: rgba(255, 235, 59, 0.3) !important;
  color: #fff !important;
  border: 1px solid rgba(255, 235, 59, 0.5) !important;
  box-shadow: none !important;
  padding: 0.1em 0.3em !important;
  border-radius: 3px !important;
}

/* 高亮文本在表格中的样式 */
table mark, table .highlight {
  font-size: 0.9em;
  padding: 0.1em 0.3em;
  margin: 0 0.1em;
}

/* 高亮文本在标题中的样式 */
h1 mark, h1 .highlight,
h2 mark, h2 .highlight,
h3 mark, h3 .highlight,
h4 mark, h4 .highlight,
h5 mark, h5 .highlight,
h6 mark, h6 .highlight {
  font-size: 0.9em;
  vertical-align: baseline;
  line-height: 1;
}

/* 深色模式下的高亮样式 */
@media (prefers-color-scheme: dark) {
  mark, .highlight {
    background: linear-gradient(135deg, rgba(255, 235, 59, 0.4) 0%, rgba(255, 193, 7, 0.3) 100%);
    color: #fff;
    border-color: rgba(255, 193, 7, 0.5);
  }

  mark.cyan, .highlight-cyan {
    background: linear-gradient(135deg, rgba(63, 207, 187, 0.4) 0%, rgba(51, 202, 187, 0.3) 100%);
    color: #4dd4c7;
    border-color: rgba(63, 207, 187, 0.5);
  }

  mark.red, .highlight-red {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.4) 0%, rgba(211, 47, 47, 0.3) 100%);
    color: #ff8a80;
    border-color: rgba(244, 67, 54, 0.5);
  }

  mark.green, .highlight-green {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.4) 0%, rgba(56, 142, 60, 0.3) 100%);
    color: #a5d6a7;
    border-color: rgba(76, 175, 80, 0.5);
  }

  mark.blue, .highlight-blue {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.4) 0%, rgba(25, 118, 210, 0.3) 100%);
    color: #90caf9;
    border-color: rgba(33, 150, 243, 0.5);
  }

  mark.purple, .highlight-purple {
    background: linear-gradient(135deg, rgba(156, 39, 176, 0.4) 0%, rgba(123, 31, 162, 0.3) 100%);
    color: #ce93d8;
    border-color: rgba(156, 39, 176, 0.5);
  }

  mark.orange, .highlight-orange {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.4) 0%, rgba(245, 124, 0, 0.3) 100%);
    color: #ffcc80;
    border-color: rgba(255, 152, 0, 0.5);
  }

  mark.gray, .highlight-gray {
    background: linear-gradient(135deg, rgba(158, 158, 158, 0.4) 0%, rgba(117, 117, 117, 0.3) 100%);
    color: #e0e0e0;
    border-color: rgba(158, 158, 158, 0.5);
  }
}

/* 手动深色模式下的高亮样式 */
body.dark-mode mark, body.dark-mode .highlight {
  background: linear-gradient(135deg, rgba(255, 235, 59, 0.4) 0%, rgba(255, 193, 7, 0.3) 100%);
  color: #fff;
  border-color: rgba(255, 193, 7, 0.5);
}

body.dark-mode mark.cyan, body.dark-mode .highlight-cyan {
  background: linear-gradient(135deg, rgba(63, 207, 187, 0.4) 0%, rgba(51, 202, 187, 0.3) 100%);
  color: #4dd4c7;
  border-color: rgba(63, 207, 187, 0.5);
}

body.dark-mode mark.red, body.dark-mode .highlight-red {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.4) 0%, rgba(211, 47, 47, 0.3) 100%);
  color: #ff8a80;
  border-color: rgba(244, 67, 54, 0.5);
}

body.dark-mode mark.green, body.dark-mode .highlight-green {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.4) 0%, rgba(56, 142, 60, 0.3) 100%);
  color: #a5d6a7;
  border-color: rgba(76, 175, 80, 0.5);
}

body.dark-mode mark.blue, body.dark-mode .highlight-blue {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.4) 0%, rgba(25, 118, 210, 0.3) 100%);
  color: #90caf9;
  border-color: rgba(33, 150, 243, 0.5);
}

body.dark-mode mark.purple, body.dark-mode .highlight-purple {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.4) 0%, rgba(123, 31, 162, 0.3) 100%);
  color: #ce93d8;
  border-color: rgba(156, 39, 176, 0.5);
}

body.dark-mode mark.orange, body.dark-mode .highlight-orange {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.4) 0%, rgba(245, 124, 0, 0.3) 100%);
  color: #ffcc80;
  border-color: rgba(255, 152, 0, 0.5);
}

body.dark-mode mark.gray, body.dark-mode .highlight-gray {
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.4) 0%, rgba(117, 117, 117, 0.3) 100%);
  color: #e0e0e6;
  border-color: rgba(158, 158, 158, 0.5);
}

/* 移动端高亮文本优化 */
@media (max-width: 768px) {
  mark, .highlight {
    padding: 0.15em 0.3em;
    font-size: 0.95em;
    border-radius: 3px;
  }

  h1 mark, h1 .highlight,
  h2 mark, h2 .highlight,
  h3 mark, h3 .highlight {
    font-size: 0.85em;
    padding: 0.1em 0.25em;
  }

  table mark, table .highlight {
    font-size: 0.8em;
    padding: 0.05em 0.2em;
  }
}

/* 高亮文本选择效果 */
mark::selection, .highlight::selection {
  background: rgba(255, 255, 255, 0.3);
}

mark::-moz-selection, .highlight::-moz-selection {
  background: rgba(255, 255, 255, 0.3);
}
