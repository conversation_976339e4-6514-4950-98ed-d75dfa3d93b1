<!-- 主标题 -->
<div style="text-align: center; margin: 10px 0 30px 0;">
  <h1 style="font-size: 5em; font-weight: 600; margin: 0; color: #2aa698; letter-spacing: 0.05em; text-shadow: 0 2px 8px rgba(42, 166, 152, 0.2);">一花CCPROXY系统商业版</h1>
</div>

## 🚀 敢为人先的CCPROXY管理平台

> 让管理不在复制，让功能更加多样

**✨ 核心功能**
- 🤖 **代理商管理** - 多级代理商系统，支持代理商自助管理和销售
- ⚡ **卡密系统** - 自动生成卡密，支持批量操作和导出
- 🔒 **服务器管理** - 便捷管理多个CCProxy服务器实例
- 🌐 **应用配置** - 灵活配置应用参数和服务器连接信息
- 🍔 **套餐管理** - 灵活配置套餐参数，支持Markdown文档
- 📊 **用户管理** - 用户账号创建、控制和管理、密码修改、清理过期用户，优化补偿加时等
- 🎨 **个性化定制** - 灵活配置，满足个性需求

<!-- 统计信息 - 简约3D风格 -->
<div style="text-align: center; margin: 40px 0 30px 0;">
  <div style="display: inline-flex; gap: 16px; flex-wrap: wrap; justify-content: center;">
    <span style="background: linear-gradient(145deg, #e8f4f8 0%, #f0f9fc 100%); color: #2aa698; padding: 10px 18px; border-radius: 16px; font-size: 0.9em; font-weight: 600; box-shadow: 0 6px 16px rgba(63, 207, 187, 0.15), 0 2px 4px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.8), inset 0 -1px 0 rgba(63,207,187,0.1); transform: perspective(400px) rotateX(3deg); border: 1px solid rgba(255,255,255,0.6);">📊 文档字数: 9.4k+</span>
    <span style="background: linear-gradient(145deg, #e8f4f8 0%, #f0f9fc 100%); color: #2aa698; padding: 10px 18px; border-radius: 16px; font-size: 0.9em; font-weight: 600; box-shadow: 0 6px 16px rgba(63, 207, 187, 0.15), 0 2px 4px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.8), inset 0 -1px 0 rgba(63,207,187,0.1); transform: perspective(400px) rotateX(3deg); border: 1px solid rgba(255,255,255,0.6);">📖 章节数: 15+</span>
    <span style="background: linear-gradient(145deg, #e8f4f8 0%, #f0f9fc 100%); color: #2aa698; padding: 10px 18px; border-radius: 16px; font-size: 0.9em; font-weight: 600; box-shadow: 0 6px 16px rgba(63, 207, 187, 0.15), 0 2px 4px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.8), inset 0 -1px 0 rgba(63,207,187,0.1); transform: perspective(400px) rotateX(3deg); border: 1px solid rgba(255,255,255,0.6);">⏱️ 阅读时长: 48分钟</span>
    <span style="background: linear-gradient(145deg, #e8f4f8 0%, #f0f9fc 100%); color: #2aa698; padding: 10px 18px; border-radius: 16px; font-size: 0.9em; font-weight: 600; box-shadow: 0 6px 16px rgba(63, 207, 187, 0.15), 0 2px 4px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.8), inset 0 -1px 0 rgba(63,207,187,0.1); transform: perspective(400px) rotateX(3deg); border: 1px solid rgba(255,255,255,0.6);">🔄 最后更新: 2025</span>
  </div>
</div>

<!-- 按钮组 -->
<div style="margin: 40px 0; display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
  <a href="#/README" style="display: inline-block; padding: 15px 30px; background: linear-gradient(135deg, #3fcfbb 0%, #2aa698 100%); color: #ffffff; text-decoration: none; border-radius: 25px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(63, 207, 187, 0.3); border: none;">🚀 立即开始</a>
  <a href="#/README" style="display: inline-block; padding: 15px 30px; border: 2px solid #3fcfbb; color: #2aa698; background: rgba(255, 255, 255, 0.9); text-decoration: none; border-radius: 25px; font-weight: 600; transition: all 0.3s ease;">📚 查看文档</a>
  <a href="https://github.com/yeuxuan/yihua_ccproxy_business" style="display: inline-block; padding: 15px 30px; border: 2px solid #33cabb; color: #2aa698; background: rgba(255, 255, 255, 0.9); text-decoration: none; border-radius: 25px; font-weight: 600; transition: all 0.3s ease;">💻 GitHub</a>
</div>

<!-- 背景色 -->
![color](#1a1a2e)
